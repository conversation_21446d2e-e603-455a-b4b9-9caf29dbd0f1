import 'package:equatable/equatable.dart';
import 'package:politics_social_app/core/network/network_api_versions.dart';
import 'package:politics_social_app/core/network/network_enum.dart';

class NetWorkRequest extends Equatable {
  final NetworkApiVersions? versions;
  final String path;
  final Map<String, String> headers;
  final NetworkMethod method;
  final int timeout;
  final Map<String, dynamic>? body;

  const NetWorkRequest({
    required this.path,
    this.body,
    this.versions,
    required this.method,
    this.timeout = 10,
    this.headers = const {},
  });

  @override
  List<Object> get props => [path];
}
