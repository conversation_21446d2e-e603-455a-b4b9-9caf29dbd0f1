import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'locale_keys.g.dart';

class L10n {
  static const List<Locale> support = [Locale("fr", "FR"), Locale("en", "UK")];
  static const defaultLocale = Locale("fr", "FR");
}

enum SupportedLanguage { english, french }

extension SupportedLanguageExtension on SupportedLanguage {
  String get name {
    switch (this) {
      case SupportedLanguage.french:
        return LocaleKeys.supported_language_french.tr();
      case SupportedLanguage.english:
        return LocaleKeys.supported_language_english.tr();
    }
  }

  Locale get locale {
    switch (this) {
      case SupportedLanguage.french:
        return Locale("fr", "FR");
      case SupportedLanguage.english:
        return Locale("en", "UK");
    }
  }
}

extension LocaleExtension on Locale {
  SupportedLanguage toSupportedLanguage() {
    switch (languageCode) {
      case "fr":
        return SupportedLanguage.french;
      case "en":
        return SupportedLanguage.english;
      default:
        return SupportedLanguage.french;
    }
  }
}
