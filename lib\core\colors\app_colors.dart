import 'package:flutter/material.dart';

class AppColors {
  static const Color primaryColor = Color(0xFFE26929);
  static const Color secondaryColor = Color(0xFFFF3F05);
  static const Color background = Color(0xFFFFFFFF);
  static const Color darkBackground = Color(0xFF000000);
  static const Color white = Color(0xFFFFFFFF);
  static const Color btnDesable = Color(0xFF808080);
  static const Color borderColor = Color(0xFF515978);

  //Colors Orange
  static const Color orange6416C = Color(0xFFF05B42);
  static const Color orange05B42 = Color(0xFFF6416C);

  static const Color lightOrangeBackground = Color(0xFFFFE9D4);
  static const Color orange5B42 = Color(0xFFF05B42);
  static const Color orange416C = Color(0xFFF6416C);
  static const Color orangeE9D4 = Color(0xFFFFE9D4);

  static const Color darkRed = Color(0xFF782E21);

  //Colors black
  static const Color darkColor161C2B = Color(0xFF161C2B);
  static const Color darkColor515978 = Color(0xFF515978);
  static const Color darkColor090909 = Color(0xFF090909);
  static const Color almostBlack = Color(0xFF222222);

  //Color grey
  static const Color grey = Color(0xFF9E9E9E);
  static const Color grey2F2F7 = Color(0xFFF2F2F7);
  static const Color colorLightGray = Color(0xFFEFF2F4);
  static const Color greyText = Color(0xFF475569);

  //Color brown
  static const Color brown782E21 = Color(0xFF782E21);

  static const greyArgent = Color(0xFFA8A8A8);
  static const greyBold = Color(0xFF595959);
  static const greyBlue = Color(0xFF515978);

  static const Color colorBlueGray = Color(0xFF677294);
  static const Color colorBlueGrayLight = Color(0xFFF3F4F6);

  static const Color marronClair = Color(0xFFBF8D7A);

  static const Color colorSuccess = Color.fromARGB(255, 6, 168, 0);
  static const Color colorError = Color.fromARGB(255, 255, 34, 0);
  static const Color colorWarning = Color.fromARGB(255, 255, 139, 7);
}
