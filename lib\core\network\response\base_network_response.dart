class DefaultNetworkResponse<T> {
  T? data;

  DefaultNetworkResponse({this.data});

  factory DefaultNetworkResponse.fromJson(
    Map<String, dynamic> json,
    Function(dynamic)? fromJson,
  ) {
    return DefaultNetworkResponse<T>(
      data: json['data'] == null ? null : fromJson?.call(json['data']),
    );
  }

  Map<String, dynamic> toJson(Function(T)? toJson) {
    return {'data': data == null ? null : toJson?.call(data as T)};
  }
}
