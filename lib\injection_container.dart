import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart';
import 'package:politics_social_app/core/environnement/environment.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'core/network/network_info.dart';
import 'core/network/network_service.dart';
import 'package:http/http.dart' as http;

final locator = GetIt.instance;

Future<void> injectDependencies(Environnement environnement) async {
  //! External
  final sharedPref = await SharedPreferences.getInstance();
  locator.registerFactory<SharedPreferences>(() => sharedPref);
  locator.registerLazySingleton<Client>(() => http.Client());
  //locator.registerFactory<FirebaseAnalytics>(() => FirebaseAnalytics.instance);

  //! Core
  locator.registerSingleton<Environnement>(environnement);

  locator.registerLazySingleton<NetworkInfo>(
    () => NetworkInfoImpl(connectivity: Connectivity()),
  );

  locator.registerLazySingleton<NetworkService>(
    () => NetworkServiceImpl(
      sharedPreferences: locator(),
      environnement: environnement,
      httpClient: locator(),
      networkInfo: locator(),
    ),
  );
}
