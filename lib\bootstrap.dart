import 'dart:async';
import 'dart:developer';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter/material.dart';
import 'package:politics_social_app/core/environnement/env.dart';
import 'package:politics_social_app/core/environnement/environment.dart';
import 'package:politics_social_app/core/logger/lunion_educ_logger.dart';
import 'package:politics_social_app/l10n/support_locale.dart';
import 'injection_container.dart' as di;

/// Bootstrap global de l'application avec Provider
Future<void> bootstrap({
  required Environnement environnement,
  required FutureOr<Widget> Function() builder,
}) async {
  WidgetsFlutterBinding.ensureInitialized();

  /// Gérer les erreurs Flutter
  FlutterError.onError = (details) {
    if (kDebugMode) {
      log(details.exceptionAsString(), stackTrace: details.stack);
    }
    // TODO: Ajouter Crashlytics ici si nécessaire
    // FirebaseCrashlytics.instance.recordFlutterError(details);
  };

  /// Initialiser l'environnement
  Env.init(environnement);

  /// Initialiser la localisation
  await EasyLocalization.ensureInitialized();

  /// Injection des dépendances
  await di.injectDependencies(environnement);

  /// Démarrer l'application dans une zone sécurisée
  await runZonedGuarded(
    () async {
      runApp(
        EasyLocalization(
          supportedLocales: L10n.support,
          path: 'assets/translations',
          fallbackLocale: L10n.defaultLocale,
          child: await builder(),
        ),
      );
    },
    (error, stackTrace) {
      if (kDebugMode) {
        LunionEducLogger.e('❌ Zone Error: $error');
      }
    },
  );
}
