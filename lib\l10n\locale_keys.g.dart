// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

// ignore_for_file: constant_identifier_names

abstract class  LocaleKeys {
  static const supported_language_french = 'supported_language_french';
  static const supported_language_english = 'supported_language_english';
  static const common_skip = 'common_skip';
  static const common_next = 'common_next';
  static const common_start = 'common_start';
  static const common_sigin = 'common_sigin';
  static const common_signup = 'common_signup';
  static const common_save = 'common_save';
  static const common_delete = 'common_delete';
  static const onboarding_title1 = 'onboarding_title1';
  static const onboarding_title2 = 'onboarding_title2';
  static const onboarding_title3 = 'onboarding_title3';
  static const onboarding_welcome_title = 'onboarding_welcome_title';
  static const register_bottomsheet_already_account = 'register_bottomsheet_already_account';
  static const login_title = 'login_title';
  static const login_identifiant = 'login_identifiant';
  static const login_password = 'login_password';
  static const login_forgot_password = 'login_forgot_password';
  static const login_button = 'login_button';
  static const login_error = 'login_error';
  static const login_no_account = 'login_no_account';
  static const dashboard_title = 'dashboard_title';
  static const dashboard_welcome = 'dashboard_welcome';
  static const dashboard_school_label = 'dashboard_school_label';
  static const dashboard_continue = 'dashboard_continue';
  static const error_identifiant_required = 'error_identifiant_required';
  static const error_password_required = 'error_password_required';
  static const error_select_account_type = 'error_select_account_type';
  static const welcome_title = 'welcome_title';
  static const welcome_description = 'welcome_description';
  static const learn_more_button = 'learn_more_button';
  static const my_space_button = 'my_space_button';
  static const register_title = 'register_title';
  static const student_id_hint = 'student_id_hint';
  static const school_code_hint = 'school_code_hint';
  static const register_student_message_box = 'register_student_message_box';
  static const register_password_title = 'register_password_title';
  static const new_password_hint = 'new_password_hint';
  static const confirm_password_hint = 'confirm_password_hint';
  static const register_password_message_box = 'register_password_message_box';
  static const register_select_account_type = 'register_select_account_type';
  static const enum_user_type_student = 'enum_user_type_student';
  static const enum_user_type_parent = 'enum_user_type_parent';
  static const register_select_country_placeholder = 'register_select_country_placeholder';
  static const register_already_account_text = 'register_already_account_text';
  static const our_vision = 'our_vision';
  static const our_vision_description = 'our_vision_description';
  static const key_features = 'key_features';
  static const transformative_advantages = 'transformative_advantages';
  static const ready_to_revolutionize = 'ready_to_revolutionize';
  static const join_thousands = 'join_thousands';
  static const intro_text = 'intro_text';
  static const feature_personalized_learning = 'feature_personalized_learning';
  static const feature_personalized_learning_desc = 'feature_personalized_learning_desc';
  static const feature_admin = 'feature_admin';
  static const feature_admin_desc = 'feature_admin_desc';
  static const feature_engagement = 'feature_engagement';
  static const feature_engagement_desc = 'feature_engagement_desc';
  static const feature_adaptive_content = 'feature_adaptive_content';
  static const feature_adaptive_content_desc = 'feature_adaptive_content_desc';
  static const advantage_performance = 'advantage_performance';
  static const advantage_performance_desc = 'advantage_performance_desc';
  static const advantage_time = 'advantage_time';
  static const advantage_time_desc = 'advantage_time_desc';
  static const advantage_motivation = 'advantage_motivation';
  static const advantage_motivation_desc = 'advantage_motivation_desc';
  static const advantage_goals = 'advantage_goals';
  static const advantage_goals_desc = 'advantage_goals_desc';
  static const advantage_accessibility = 'advantage_accessibility';
  static const advantage_accessibility_desc = 'advantage_accessibility_desc';
  static const advantage_analytics = 'advantage_analytics';
  static const advantage_analytics_desc = 'advantage_analytics_desc';
  static const register_infos_title = 'register_infos_title';
  static const register_infos_confirm = 'register_infos_confirm';
  static const forgot_password_title = 'forgot_password_title';
  static const forgot_password_birthdate = 'forgot_password_birthdate';
  static const forgot_password_continue = 'forgot_password_continue';
  static const error_birthdate_required = 'error_birthdate_required';
  static const error_birthdate_format = 'error_birthdate_format';
  static const forgot_password_success = 'forgot_password_success';
  static const forgot_password_error = 'forgot_password_error';
  static const register_parent_title = 'register_parent_title';
  static const register_parent_lastname_hint = 'register_parent_lastname_hint';
  static const register_parent_firstname_hint = 'register_parent_firstname_hint';
  static const register_parent_address_hint = 'register_parent_address_hint';
  static const register_parent_civility_placeholder = 'register_parent_civility_placeholder';
  static const register_parent_profession_placeholder = 'register_parent_profession_placeholder';
  static const register_parent_phone_hint = 'register_parent_phone_hint';
  static const civility_mr = 'civility_mr';
  static const civility_mrs = 'civility_mrs';
  static const civility_miss = 'civility_miss';
  static const profession_developer = 'profession_developer';
  static const profession_doctor = 'profession_doctor';
  static const register_add_student_title = 'register_add_student_title';
  static const register_add_student_matricule_hint = 'register_add_student_matricule_hint';
  static const register_add_student_birthdate_hint = 'register_add_student_birthdate_hint';
  static const register_verification_in_progress = 'register_verification_in_progress';
  static const register_add_another_child = 'register_add_another_child';
  static const register_accept_condition_title = 'register_accept_condition_title';
  static const register_accept_condition_description = 'register_accept_condition_description';
  static const common_yes = 'common_yes';
  static const common_no = 'common_no';
  static const enter_otp_code = 'enter_otp_code';
  static const resend_code = 'resend_code';
  static const phone_required = 'phone_required';
  static const otp_not_received = 'otp_not_received';
  static const register_form_lastname = 'register_form.lastname';
  static const register_form_lastname_required = 'register_form.lastname_required';
  static const register_form_firstname = 'register_form.firstname';
  static const register_form_firstname_required = 'register_form.firstname_required';
  static const register_form_email = 'register_form.email';
  static const register_form_email_required = 'register_form.email_required';
  static const register_form_email_invalid = 'register_form.email_invalid';
  static const register_form_phone = 'register_form.phone';
  static const register_form_phone_required = 'register_form.phone_required';
  static const register_form_phone_invalid = 'register_form.phone_invalid';
  static const register_form_birthdate = 'register_form.birthdate';
  static const register_form_birthdate_required = 'register_form.birthdate_required';
  static const register_form_gender = 'register_form.gender';
  static const register_form_gender_required = 'register_form.gender_required';
  static const register_form_country = 'register_form.country';
  static const register_form_country_required = 'register_form.country_required';
  static const register_form_city = 'register_form.city';
  static const register_form_city_required = 'register_form.city_required';
  static const register_form_commune = 'register_form.commune';
  static const register_form_commune_required = 'register_form.commune_required';
  static const register_form_continue = 'register_form.continue';
  static const register_form_phone_description = 'register_form.phone_description';
  static const register_form_already_account = 'register_form.already_account';
  static const register_form = 'register_form';
  static const change_password_title = 'change_password_title';
  static const menu_home = 'menu.home';
  static const menu_contents = 'menu.contents';
  static const menu_school_and_me = 'menu.school_and_me';
  static const menu_favorites = 'menu.favorites';
  static const menu_menu = 'menu.menu';
  static const menu = 'menu';
  static const home_hello = 'home.hello';
  static const home_search_hint = 'home.search_hint';
  static const home_my_space = 'home.my_space';
  static const home_schedule = 'home.schedule';
  static const home_ads_space = 'home.ads_space';
  static const home_quick_actions = 'home.quick_actions';
  static const home_news = 'home.news';
  static const home_no_news = 'home.no_news';
  static const home_features_registration = 'home.features.registration';
  static const home_features_courses = 'home.features.courses';
  static const home_features_absences = 'home.features.absences';
  static const home_features_documents = 'home.features.documents';
  static const home_features = 'home.features';
  static const home = 'home';
  static const school_type_student = 'school_type.student';
  static const school_type_college = 'school_type.college';
  static const school_type_technical = 'school_type.technical';
  static const school_type_university = 'school_type.university';
  static const school_type_training_center = 'school_type.training_center';
  static const school_type = 'school_type';
  static const school_type_title = 'school_type_title';
  static const registration_sheet_title = 'registration_sheet.title';
  static const registration_sheet_manual_verification = 'registration_sheet.manual_verification';
  static const registration_sheet_inscription_title = 'registration_sheet.inscription.title';
  static const registration_sheet_inscription_subtitle = 'registration_sheet.inscription.subtitle';
  static const registration_sheet_inscription = 'registration_sheet.inscription';
  static const registration_sheet_reinscription_title = 'registration_sheet.reinscription.title';
  static const registration_sheet_reinscription_subtitle = 'registration_sheet.reinscription.subtitle';
  static const registration_sheet_reinscription = 'registration_sheet.reinscription';
  static const registration_sheet_transfer_title = 'registration_sheet.transfer.title';
  static const registration_sheet_transfer_subtitle = 'registration_sheet.transfer.subtitle';
  static const registration_sheet_transfer = 'registration_sheet.transfer';
  static const registration_sheet = 'registration_sheet';
  static const register_otp_validation = 'register_otp_validation';
  static const register_form_phone_dial_code_error = 'register_form_phone_dial_code_error';
  static const registration_identify_student_title = 'registration_identify_student.title';
  static const registration_identify_student_student_form = 'registration_identify_student.student_form';
  static const registration_identify_student_required_fields_notice = 'registration_identify_student.required_fields_notice';
  static const registration_identify_student_step_title = 'registration_identify_student.step_title';
  static const registration_identify_student_identity_section = 'registration_identify_student.identity_section';
  static const registration_identify_student_identity_description = 'registration_identify_student.identity_description';
  static const registration_identify_student_full_name = 'registration_identify_student.full_name';
  static const registration_identify_student_country = 'registration_identify_student.country';
  static const registration_identify_student_city = 'registration_identify_student.city';
  static const registration_identify_student_district = 'registration_identify_student.district';
  static const registration_identify_student_birth_date = 'registration_identify_student.birth_date';
  static const registration_identify_student_contact = 'registration_identify_student.contact';
  static const registration_identify_student_email = 'registration_identify_student.email';
  static const registration_identify_student_gender = 'registration_identify_student.gender';
  static const registration_identify_student_guardian_name = 'registration_identify_student.guardian_name';
  static const registration_identify_student_guardian_profession = 'registration_identify_student.guardian_profession';
  static const registration_identify_student_guardian_contact = 'registration_identify_student.guardian_contact';
  static const registration_identify_student = 'registration_identify_student';
  static const notifications = 'notifications';
  static const congrats = 'congrats';
  static const inscription_approved_message = 'inscription_approved_message';
  static const go_to_payment = 'go_to_payment';
  static const soumettre = 'soumettre';
  static const quittance = 'quittance';
  static const quittance_notice = 'quittance_notice';
  static const payment_details = 'payment_details';
  static const total_to_pay = 'total_to_pay';
  static const total = 'total';
  static const choose_payment_method = 'choose_payment_method';
  static const mobile_money = 'mobile_money';
  static const mobile_money_description = 'mobile_money_description';
  static const credit_card = 'credit_card';
  static const credit_card_description = 'credit_card_description';
  static const finalize_payment = 'finalize_payment';
  static const pay = 'pay';
  static const choose_operator = 'choose_operator';
  static const enter_mobile_money_number = 'enter_mobile_money_number';
  static const choose_card = 'choose_card';
  static const registration_formation_student_formation = 'registration_formation_student.formation';
  static const registration_formation_student_formation_description = 'registration_formation_student.formation_description';
  static const registration_formation_student_choose_establishment = 'registration_formation_student.choose_establishment';
  static const registration_formation_student_choose_level = 'registration_formation_student.choose_level';
  static const registration_formation_student_choose_field = 'registration_formation_student.choose_field';
  static const registration_formation_student_student_status = 'registration_formation_student.student_status';
  static const registration_formation_student_academic_year = 'registration_formation_student.academic_year';
  static const registration_formation_student_provided_documents = 'registration_formation_student.provided_documents';
  static const registration_formation_student_upload_instruction = 'registration_formation_student.upload_instruction';
  static const registration_formation_student_add_document = 'registration_formation_student.add_document';
  static const registration_formation_student_show_document = 'registration_formation_student.show_document';
  static const registration_formation_student = 'registration_formation_student';
  static const registration_resume_registration_resume_student_card_title = 'registration_resume.registration_resume_student_card_title';
  static const registration_resume_nationality = 'registration_resume.nationality';
  static const registration_resume_school_name = 'registration_resume.school_name';
  static const registration_resume_matricule = 'registration_resume.matricule';
  static const registration_resume_pathway = 'registration_resume.pathway';
  static const registration_resume_submission_date = 'registration_resume.submission_date';
  static const registration_resume_payment_info = 'registration_resume.payment_info';
  static const registration_resume_registration_fee = 'registration_resume.registration_fee';
  static const registration_resume_submit = 'registration_resume.submit';
  static const registration_resume = 'registration_resume';
  static const dialog_success_title = 'dialog.success_title';
  static const dialog_success_description = 'dialog.success_description';
  static const dialog_success_button = 'dialog.success_button';
  static const dialog_error_title = 'dialog.error_title';
  static const dialog_error_description = 'dialog.error_description';
  static const dialog_error_button = 'dialog.error_button';
  static const dialog = 'dialog';
  static const payment_confirmation = 'payment_confirmation';
  static const validate = 'validate';
  static const from = 'from';
  static const register_password_message = 'register_password_message';
  static const motifs = 'motifs';
  static const desapprobation = 'desapprobation';
  static const create_new_password = 'create_new_password';
  static const option = 'option';
  static const quittance_infos = 'quittance_infos';
  static const admis = 'admis';
  static const ajourne = 'ajourne';
  static const attente = 'attente';

}
