import 'package:politics_social_app/core/utils/constates.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorage {
  static SharedPreferences? _preferences;

  LocalStorage._();

  static init() async {
    _preferences = await SharedPreferences.getInstance();
  }

  static String getToken() =>
      _preferences?.getString(AppConstants.keyToken) ?? '';

  static void deleteToken() => _preferences?.remove(AppConstants.keyToken);

  static void clearStore() {
    deleteToken();
  }
}
