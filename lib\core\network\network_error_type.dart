enum NetworkErrorType {
  badRequestException,
  badGeteway,
  fetchDataException,
  notFoundException,
  internalServerError,
  noInternetConnection,
  forbidden,
  platformMissing,
  unknown;

  static NetworkErrorType fromCode(int code) {
    switch (code) {
      case 400:
        return NetworkErrorType.badRequestException;
      case 403:
        return NetworkErrorType.forbidden;
      case 404:
        return NetworkErrorType.notFoundException;
      case 500:
        return NetworkErrorType.internalServerError;
      case 502:
        return NetworkErrorType.badGeteway;
      default:
        return NetworkErrorType.unknown;
    }
  }
}
