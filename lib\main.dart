import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:politics_social_app/core/environnement/environment.dart';
import 'package:politics_social_app/core/routes/routes.dart';
import 'package:politics_social_app/core/storage/local_storage.dart';
import 'package:politics_social_app/l10n/support_locale.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'injection_container.dart' as di;

final GlobalKey<NavigatorState> navigatorKey = GlobalKey(
  debugLabel: "Main Navigator",
);

void main() async {
  /// Localization
  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();
  await LocalStorage.init();

  /// Dependencies injections
  await di.injectDependencies(EnvironnementDev());

  runApp(
    EasyLocalization(
      supportedLocales: L10n.support,
      path: 'assets/translations',
      fallbackLocale: L10n.defaultLocale,
      child: LunionEducApp(),
    ),
  );
}

class LunionEducApp extends StatefulWidget {
  const LunionEducApp({super.key});
  @override
  LunionEducAppState createState() => LunionEducAppState();
}

class LunionEducAppState extends State<LunionEducApp>
    with WidgetsBindingObserver {
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setPreferredOrientations(<DeviceOrientation>[
      DeviceOrientation.portraitUp,
    ]);
    return MultiProvider(
      providers: _LunionEducAppMultiProvider.providers(context),
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        theme: ThemeData.light(useMaterial3: true),
        navigatorKey: navigatorKey,
        locale: context.locale,
        supportedLocales: context.supportedLocales,
        localizationsDelegates: context.localizationDelegates,
        fallbackLocale: context.fallbackLocale,
        initialRoute: "/",
        getPages: routes(),
        title: "Politics Social App",
      ),
    );
  }
}

class _LunionEducAppMultiProvider {
  static List<SingleChildWidget> providers(BuildContext context) {
    return [
      // ChangeNotifierProvider(create: (context) => SplashViewModel()),
    ];
  }
}
