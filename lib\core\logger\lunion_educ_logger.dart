import 'package:logger/logger.dart';

enum LunionEducLoggerLevelType { verbose, debug, info, warning, error }

abstract class LunionEducLogger {
  static void v(String message, [dynamic error, StackTrace? stackTrace]) {
    log(message, LunionEducLoggerLevelType.verbose, error, stackTrace);
  }

  static void d(String message, [dynamic error, StackTrace? stackTrace]) {
    log(message, LunionEducLoggerLevelType.debug, error, stackTrace);
  }

  static void i(String message, [dynamic error, StackTrace? stackTrace]) {
    log(message, LunionEducLoggerLevelType.info, error, stackTrace);
  }

  static void w(String message, [dynamic error, StackTrace? stackTrace]) {
    log(message, LunionEducLoggerLevelType.warning, error, stackTrace);
  }

  static void e(String message, [dynamic error, StackTrace? stackTrace]) {
    log(message, LunionEducLoggerLevelType.error, error, stackTrace);
  }

  static void log(
    String message,
    LunionEducLoggerLevelType type, [
    dynamic error,
    StackTrace? stackTrace,
  ]) {
    var logger = Logger();
    switch (type) {
      case LunionEducLoggerLevelType.debug:
        logger.d(message);
        break;
      case LunionEducLoggerLevelType.info:
        logger.i(message);
        break;
      case LunionEducLoggerLevelType.warning:
        logger.w(message);
        break;
      case LunionEducLoggerLevelType.error:
        logger.e(message);
        break;
      default:
        break;
    }
  }
}
