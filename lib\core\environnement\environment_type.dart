part of 'environment.dart';

enum EnvironmentType {
  localHost,
  dev,
  staging,
  prod;

  String get name {
    switch (this) {
      case EnvironmentType.localHost:
        return "Local";
      case EnvironmentType.dev:
        return "Dev";
      case EnvironmentType.staging:
        return "Stag";
      case EnvironmentType.prod:
        return "Prod";
    }
  }

  String get fullName {
    switch (this) {
      case EnvironmentType.localHost:
        return "Localhost";
      case EnvironmentType.dev:
        return "Development";
      case EnvironmentType.staging:
        return "Staging";
      case EnvironmentType.prod:
        return "Production";
    }
  }
}
