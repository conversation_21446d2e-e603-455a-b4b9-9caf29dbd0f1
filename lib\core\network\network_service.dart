import 'dart:convert';
import 'package:dartz/dartz.dart';
import 'package:http/http.dart' as http;
import 'package:http/http.dart';
import 'package:politics_social_app/core/environnement/environment.dart';
import 'package:politics_social_app/core/logger/lunion_educ_logger.dart';
import 'package:politics_social_app/core/network/network_enum.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'network_error.dart';
import 'network_info.dart';
import 'network_param.dart';

abstract class NetworkService<NetWorkParam> {
  Future<Either<NetworkError, dynamic>> fetchRequest(NetWorkRequest request);
}

class NetworkServiceImpl extends NetworkService<NetWorkRequest> {
  final Environnement environnement;
  final Client httpClient;
  final NetworkInfo networkInfo;
  final SharedPreferences sharedPreferences;
  NetworkServiceImpl({
    required this.httpClient,
    required this.environnement,
    required this.networkInfo,
    required this.sharedPreferences,
  });
  @override
  Future<Either<NetworkError, Map<String, dynamic>>> fetchRequest(
    NetWorkRequest request,
  ) async {
    if (!(await networkInfo.isConnected)) {
      return Left(_noInternetError());
    }
    try {
      switch (request.method) {
        case NetworkMethod.get:
          {
            return await _getRequest(request);
          }
        case NetworkMethod.post:
          {
            LunionEducLogger.d(
              "==================== request data ====================",
            );
            LunionEducLogger.e("Path:", request.path);
            LunionEducLogger.e("Body:", request.body);
            return await _postRequest(request);
          }
        case NetworkMethod.put:
          return await _putRequest(request);
        case NetworkMethod.delete:
          return await _deleteRequest(request);
      }
    } on Error catch (error) {
      LunionEducLogger.e("NetworkServiceImpl - An error occured :", error);
      throw UnimplementedError(error.toString());
    }
  }

  String _baseUrlFrom({required NetWorkRequest request}) {
    final version = request.versions?.value ?? "";
    final versionValue = version.isNotEmpty ? "/$version" : "";
    return environnement.baseUrl + versionValue + request.path;
  }

  Future<Either<NetworkError, Map<String, dynamic>>> _getRequest(
    NetWorkRequest request,
  ) async {
    final url = Uri.parse(_baseUrlFrom(request: request));
    final response = await http.get(
      url,
      headers: await _headersFrom(request.headers),
    );
    return _response(response);
  }

  Future<Either<NetworkError, Map<String, dynamic>>> _postRequest(
    NetWorkRequest request,
  ) async {
    final url = Uri.parse(_baseUrlFrom(request: request));

    final response = await http.post(
      url,
      body: request.body != null ? json.encode(request.body) : null,
      headers: await _headersFrom(request.headers),
    );

    return _response(response);
  }

  Future<Either<NetworkError, Map<String, dynamic>>> _putRequest(
    NetWorkRequest request,
  ) async {
    final url = Uri.parse(_baseUrlFrom(request: request));
    final response = await http.put(
      url,
      body: request.body != null ? json.encode(request.body) : null,
      headers: await _headersFrom(request.headers),
    );
    return _response(response);
  }

  Future<Either<NetworkError, Map<String, dynamic>>> _deleteRequest(
    NetWorkRequest request,
  ) async {
    final url = Uri.parse(_baseUrlFrom(request: request));
    final body = request.body != null ? json.encode(request.body) : null;
    final response = await http.delete(
      url,
      body: body,
      headers: await _headersFrom(request.headers),
    );
    return _response(response);
  }

  Future<Map<String, String>> _headersFrom(Map<String, String> headers) async {
    final Map<String, String> _headers = Map.from(headers);
    final String token = "LocalStorage.getToken()";
    _headers["Content-Type"] = 'application/json';
    if (token.isNotEmpty) {
      _headers["Authorization"] = "Bearer $token";
    }
    return _headers;
  }

  NetworkError _noInternetError() {
    return NetworkError(success: false, message: "Aucune connexion internet.");
  }

  Either<NetworkError, Map<String, dynamic>> _response(http.Response response) {
    LunionEducLogger.e(
      "==================== request response ====================",
    );
    LunionEducLogger.e("Endpoint: ${response.request?.url}");
    LunionEducLogger.e("StatusCode: ${response.statusCode}");
    LunionEducLogger.e("Body: ${response.body}");

    switch (response.statusCode) {
      case 200:
      case 201:
      case 206:
        if (response.body.isEmpty) {
          return Right({'data': null});
        }

        try {
          final decoded = jsonDecode(response.body.toString());

          if (decoded is Map<String, dynamic>) {
            return Right({'data': decoded});
          } else {
            return Right({'data': decoded});
          }
        } catch (e) {
          // Si le body n'est pas un JSON valide
          return Right({'data': response.body.toString()});
        }

      default:
        return Left(_errorFrom(response));
    }
  }

  NetworkError _errorFrom(http.Response response) {
    Map<String, dynamic> jsonResult = jsonDecode(response.body.toString());
    jsonResult['statusCode'] = jsonResult['statusCode'] == null
        ? jsonResult['statusCode']
        : response.statusCode;
    return NetworkError.fromJson(json: jsonResult);
  }
}
