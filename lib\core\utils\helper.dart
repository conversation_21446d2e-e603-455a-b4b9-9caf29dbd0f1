import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:politics_social_app/core/colors/app_colors.dart';
import 'package:politics_social_app/l10n/locale_keys.g.dart';

class Helper {
  static BuildContext? _verificationDialogContext;

  static Future<void> showVerificationDialog(BuildContext context) async {
    await showDialog(
      context: context,
      useRootNavigator: true,
      barrierDismissible: false,
      // ignore: deprecated_member_use
      barrierColor: Colors.black.withOpacity(0.8),
      builder: (dialogContext) {
        _verificationDialogContext = dialogContext;
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          backgroundColor: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    const SizedBox(
                      width: 70,
                      height: 70,
                      child: CircularProgressIndicator(
                        strokeWidth: 10,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.primaryColor,
                        ),
                      ),
                    ),
                    SvgPicture.asset(
                      "assets/svg/icon_lunion_educ.svg",
                      width: 24,
                      height: 24,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Text(
                  LocaleKeys.register_verification_in_progress.tr(),
                  style: Theme.of(
                    dialogContext,
                  ).textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
        );
      },
    );

    _verificationDialogContext = null;
  }

  static void closeVerificationDialog() {
    if (_verificationDialogContext != null) {
      Navigator.of(_verificationDialogContext!).pop();
      _verificationDialogContext = null;
    }
  }
}
