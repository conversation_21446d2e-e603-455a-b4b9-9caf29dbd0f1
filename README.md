# 📘 Politics Social App

Application Flutter multi-plateforme (Android, iOS)

---

## 📦 Installation

1.  C<PERSON><PERSON> le projet :

    ```bash
    git clone https://github.com/LUNION-LAB-ORG/politics-social-app.git
    cd politics-social-app
    ```

2.  Installez les dépendances :

    ```bash
    flutter pub get
    ```

3.  <PERSON><PERSON><PERSON>rez les fichiers nécessaires :

    ```bash
    flutter pub run build_runner build --delete-conflicting-outputs
    ```

4.  G<PERSON><PERSON>rez les traductions :

    ```bash
    flutter pub run easy_localization:generate -S assets/translations -f keys -O lib/l10n -o locale_keys.g.dart
    ```

---

## 🚀 Lancer le projet

### ▶️ Android

| Mode       | Commande                                                     |
| ---------- | ------------------------------------------------------------ |
| Normal     | `flutter run -d android`                                     |
| Dev        | `flutter run -d android --flavor dev -t lib/main_dev.dart`   |
| Production | `flutter run -d android --flavor prod -t lib/main_prod.dart` |

---

### 🍏 iOS

> ⚠️ Nécessite macOS + Xcode

| Mode       | Commande                                                 |
| ---------- | -------------------------------------------------------- |
| Normal     | `flutter run -d ios`                                     |
| Dev        | `flutter run -d ios --flavor dev -t lib/main_dev.dart`   |
| Production | `flutter run -d ios --flavor prod -t lib/main_prod.dart` |

---

## 🎨 Thèmes & Couleurs

L'application Politics Social App propose un système de thèmes flexible, incluant des modes clair et sombre, et des couleurs et polices de caractères bien définies pour une expérience utilisateur cohérente.

### Couleurs

Les couleurs principales de l'application sont définies dans `lib/core/colors/app_colors.dart`.

| Nom de la Couleur | Valeur Hexadécimale | Utilisation                                                |
| :---------------- | :------------------ | :--------------------------------------------------------- |
| `primaryColor`    | `#E26929`           | Couleur principale de la marque              |
| `secondaryColor`  | `#FF3F05`           | Couleur secondaire |
| `background`      | `#FFFFFF`           | Fond par défaut pour le thème clair                        |
| `darkBackground`  | `#000000`           | Fond par défaut pour le thème sombre                       |

**Comment les utiliser :**

Vous pouvez accéder à ces couleurs n'importe où dans votre code Dart en important `app_colors.dart` et en utilisant les constantes `AppColors`.

```dart
import 'package:politics_social_app/core/colors/app_colors.dart';
// ...
Color myWidgetColor = AppColors.primaryColor;
```

---

### Typographie

La typographie de l'application utilise la police **SF Pro Display** et est gérée via `lib/core/theme/app_typography.dart`. Elle définit des styles de texte spécifiques pour les thèmes clair et sombre afin d'assurer une lisibilité optimale.

| Style de Texte  | Taille (Light/Dark) | Poids (Light/Dark) | Couleur (Light/Dark) | Utilisation typique                    |
| :-------------- | :------------------ | :----------------- | :------------------- | :------------------------------------- |
| `displayLarge`  | 32                  | Bold               | Noir / Blanc         | Titres très grands, accroches          |
| `headlineLarge` | 24                  | W600               | Noir / Blanc         | Titres de section importants           |
| `titleLarge`    | 20                  | W600               | Noir / Blanc         | Titres de page, titres de carte        |
| `bodyLarge`     | 16                  | Normal             | Noir / Blanc         | Corps de texte principal               |
| `bodyMedium`    | 14                  | Normal             | Noir87 / Blanc70     | Texte secondaire, descriptions courtes |
| `labelLarge`    | 12                  | W500               | Noir54 / Blanc60     | Labels de formulaire, petites légendes |

**Comment les utiliser :**

Accédez aux styles de texte via `Theme.of(context).textTheme` pour qu'ils s'adaptent automatiquement au thème actuel (clair ou sombre).

```dart
import 'package:flutter/material.dart';
// ...
Text(
  'Mon titre',
  style: Theme.of(context).textTheme.headlineLarge,
),
Text(
  'Ceci est un paragraphe.',
  style: Theme.of(context).textTheme.bodyLarge,
),
```

---

### Thèmes (Light & Dark)

Les thèmes clair et sombre sont définis dans `lib/core/theme/app_theme.dart`. Ils configurent les couleurs de fond, les couleurs primaires, les schémas de couleurs, et les styles d'AppBar en fonction du mode sélectionné.

**Thème Clair (`lightTheme`) :**

- `brightness`: `Brightness.light`
- `scaffoldBackgroundColor`: `AppColors.background` (Blanc)
- `primaryColor`: `AppColors.primaryColor`
- `textTheme`: `AppTypography.lightTextTheme`
- `colorScheme`: Schéma de couleurs clair avec `primary` et `secondary` basés sur `AppColors`.
- `appBarTheme`: Fond blanc, titre en noir, icônes `primaryColor`.

**Thème Sombre (`darkTheme`) :**

- `brightness`: `Brightness.dark`
- `scaffoldBackgroundColor`: `AppColors.darkBackground` (Noir)
- `primaryColor`: `AppColors.primaryColor`
- `textTheme`: `AppTypography.darkTextTheme`
- `colorScheme`: Schéma de couleurs sombre avec `primary` et `secondary` basés sur `AppColors`.
- `appBarTheme`: Fond noir, titre en blanc, icônes blanches.

**Comment les utiliser :**

Dans votre widget `MaterialApp`, vous devez définir `theme` et `darkTheme` en utilisant `AppTheme.lightTheme` et `AppTheme.darkTheme`. Vous pouvez également spécifier `themeMode` pour contrôler quel thème est appliqué (par exemple, `ThemeMode.system` pour suivre les paramètres de l'appareil).

```dart
import 'package:flutter/material.dart';
import 'package:lunion_educ_app/core/theme/app_theme.dart';
// ...
MaterialApp(
  title: 'Politics Social App',
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system, // Ou ThemeMode.light, ThemeMode.dark
  home: const MyHomePage(),
);
```

En utilisant cette structure, votre application s'adaptera automatiquement aux préférences de thème de l'utilisateur et maintiendra une apparence cohérente à travers toute l'interface.

---

## 🌍 Localisation (i18n)

- Dossier des traductions : `assets/translations/`
- Fichiers : `en-US.json`, `fr-FR.json`, etc.

Génération automatique des clés :

```bash
flutter pub run easy_localization:generate -S assets/translations -f keys -O lib/l10n -o locale_keys.g.dart
```

---

## 🔧 Flavors (`dev`, `prod`)

Structure recommandée :

- `lib/main_dev.dart`
- `lib/main_prod.dart`
- Configuration dans `android/app/build.gradle` :

<!-- end list -->

```gradle
productFlavors {
    dev {
        applicationIdSuffix ".dev"
        versionNameSuffix "-dev"
    }
    prod {
        // rien à ajouter ici
    }
}
```

---

## 🛠 Structure recommandée

```
lib/
├── main.dart
├── main_dev.dart
├── main_prod.dart
├── injection_container.dart
├── bootstrap.dart
├── core/
│   ├── colors/             # Defines application-wide color palette
│   ├── environnement/      # Manages environment-specific configurations (e.g., API endpoints)
│   ├── guidelines/         # Contains UI/UX guidelines and constants
│   ├── logger/             # Handles logging functionalities
│   ├── network/            # Abstractions for network requests and API communication
│   ├── routes/             # Defines application routing and navigation logic
│   ├── storages/           # Manages local data storage (e.g., Hive, SharedPreferences)
│   ├── themes/             # Holds light and dark theme definitions (typography, colors, etc.)
│   └── utils/              # Common utility functions and helpers
├── features/               # Contains self-contained modules for specific features (e.g., authentication, dashboard)
├── l10n/                   # Generated localization keys
│   └── locale_keys.g.dart
assets/
└── translations/           # Localization files for different languages
    ├── en-UK.json
    └── fr-FR.json
```


## 🔗 Liens utiles

- [Flutter](https://flutter.dev/docs)
- [Easy Localization](https://pub.dev/packages/easy_localization)

<!-- end list -->

```

```
