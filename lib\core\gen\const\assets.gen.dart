/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/amoir_ivoire.png
  AssetGenImage get amoirIvoire => const AssetGenImage('assets/images/amoir_ivoire.png');

  /// File path: assets/images/avatar.png
  AssetGenImage get avatar => const AssetGenImage('assets/images/avatar.png');

  /// File path: assets/images/background_onboarding.png
  AssetGenImage get backgroundOnboarding => const AssetGenImage('assets/images/background_onboarding.png');

  /// File path: assets/images/credit_card.png
  AssetGenImage get creditCard => const AssetGenImage('assets/images/credit_card.png');

  /// File path: assets/images/default_avatar.png
  AssetGenImage get defaultAvatar => const AssetGenImage('assets/images/default_avatar.png');

  /// File path: assets/images/felicitation.png
  AssetGenImage get felicitation => const AssetGenImage('assets/images/felicitation.png');

  /// Directory path: assets/images/flags
  $AssetsImagesFlagsGen get flags => const $AssetsImagesFlagsGen();

  /// File path: assets/images/icon_absence.png
  AssetGenImage get iconAbsence => const AssetGenImage('assets/images/icon_absence.png');

  /// File path: assets/images/icon_cours.png
  AssetGenImage get iconCours => const AssetGenImage('assets/images/icon_cours.png');

  /// File path: assets/images/icon_document.png
  AssetGenImage get iconDocument => const AssetGenImage('assets/images/icon_document.png');

  /// File path: assets/images/icon_glitter.png
  AssetGenImage get iconGlitter => const AssetGenImage('assets/images/icon_glitter.png');

  /// File path: assets/images/icon_inscription_menu.png
  AssetGenImage get iconInscriptionMenu => const AssetGenImage('assets/images/icon_inscription_menu.png');

  /// File path: assets/images/icon_lunion_educ.png
  AssetGenImage get iconLunionEduc => const AssetGenImage('assets/images/icon_lunion_educ.png');

  /// File path: assets/images/icon_paint_palette.png
  AssetGenImage get iconPaintPalette => const AssetGenImage('assets/images/icon_paint_palette.png');

  /// File path: assets/images/icon_parent.png
  AssetGenImage get iconParent => const AssetGenImage('assets/images/icon_parent.png');

  /// File path: assets/images/icon_project.png
  AssetGenImage get iconProject => const AssetGenImage('assets/images/icon_project.png');

  /// File path: assets/images/icon_registration.png
  AssetGenImage get iconRegistration => const AssetGenImage('assets/images/icon_registration.png');

  /// File path: assets/images/icon_reinscription.png
  AssetGenImage get iconReinscription => const AssetGenImage('assets/images/icon_reinscription.png');

  /// File path: assets/images/icon_student.png
  AssetGenImage get iconStudent => const AssetGenImage('assets/images/icon_student.png');

  /// File path: assets/images/icon_target.png
  AssetGenImage get iconTarget => const AssetGenImage('assets/images/icon_target.png');

  /// File path: assets/images/icon_transfer.png
  AssetGenImage get iconTransfer => const AssetGenImage('assets/images/icon_transfer.png');

  /// File path: assets/images/info1.png
  AssetGenImage get info1Png => const AssetGenImage('assets/images/info1.png');

  /// File path: assets/images/info_1.png
  AssetGenImage get info1Png_ => const AssetGenImage('assets/images/info_1.png');

  /// File path: assets/images/iocn_arrow_left.png
  AssetGenImage get iocnArrowLeft => const AssetGenImage('assets/images/iocn_arrow_left.png');

  /// File path: assets/images/jeune_fille.png
  AssetGenImage get jeuneFille => const AssetGenImage('assets/images/jeune_fille.png');

  /// File path: assets/images/transfert.png
  AssetGenImage get transfert => const AssetGenImage('assets/images/transfert.png');

  /// File path: assets/images/wallet.png
  AssetGenImage get wallet => const AssetGenImage('assets/images/wallet.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    amoirIvoire,
    avatar,
    backgroundOnboarding,
    creditCard,
    defaultAvatar,
    felicitation,
    iconAbsence,
    iconCours,
    iconDocument,
    iconGlitter,
    iconInscriptionMenu,
    iconLunionEduc,
    iconPaintPalette,
    iconParent,
    iconProject,
    iconRegistration,
    iconReinscription,
    iconStudent,
    iconTarget,
    iconTransfer,
    info1Png,
    info1Png_,
    iocnArrowLeft,
    jeuneFille,
    transfert,
    wallet,
  ];
}

class $AssetsSvgGen {
  const $AssetsSvgGen();

  /// File path: assets/svg/arrow_left.svg
  SvgGenImage get arrowLeft => const SvgGenImage('assets/svg/arrow_left.svg');

  /// File path: assets/svg/bookmark.svg
  SvgGenImage get bookmark => const SvgGenImage('assets/svg/bookmark.svg');

  /// File path: assets/svg/confirmation_paiement.svg
  SvgGenImage get confirmationPaiement => const SvgGenImage('assets/svg/confirmation_paiement.svg');

  /// File path: assets/svg/icon_bell.svg
  SvgGenImage get iconBell => const SvgGenImage('assets/svg/icon_bell.svg');

  /// File path: assets/svg/icon_clean.svg
  SvgGenImage get iconClean => const SvgGenImage('assets/svg/icon_clean.svg');

  /// File path: assets/svg/icon_download.svg
  SvgGenImage get iconDownload => const SvgGenImage('assets/svg/icon_download.svg');

  /// File path: assets/svg/icon_error.svg
  SvgGenImage get iconError => const SvgGenImage('assets/svg/icon_error.svg');

  /// File path: assets/svg/icon_glitter.svg
  SvgGenImage get iconGlitter => const SvgGenImage('assets/svg/icon_glitter.svg');

  /// File path: assets/svg/icon_inscription.svg
  SvgGenImage get iconInscription => const SvgGenImage('assets/svg/icon_inscription.svg');

  /// File path: assets/svg/icon_inscription_menu.svg
  SvgGenImage get iconInscriptionMenu => const SvgGenImage('assets/svg/icon_inscription_menu.svg');

  /// File path: assets/svg/icon_lunion_educ.svg
  SvgGenImage get iconLunionEduc => const SvgGenImage('assets/svg/icon_lunion_educ.svg');

  /// File path: assets/svg/icon_paint_palette.svg
  SvgGenImage get iconPaintPalette => const SvgGenImage('assets/svg/icon_paint_palette.svg');

  /// File path: assets/svg/icon_parent.svg
  SvgGenImage get iconParent => const SvgGenImage('assets/svg/icon_parent.svg');

  /// File path: assets/svg/icon_picture.svg
  SvgGenImage get iconPicture => const SvgGenImage('assets/svg/icon_picture.svg');

  /// File path: assets/svg/icon_project.svg
  SvgGenImage get iconProject => const SvgGenImage('assets/svg/icon_project.svg');

  /// File path: assets/svg/icon_registration_success.svg
  SvgGenImage get iconRegistrationSuccess => const SvgGenImage('assets/svg/icon_registration_success.svg');

  /// File path: assets/svg/icon_reinscription.svg
  SvgGenImage get iconReinscription => const SvgGenImage('assets/svg/icon_reinscription.svg');

  /// File path: assets/svg/icon_search.svg
  SvgGenImage get iconSearch => const SvgGenImage('assets/svg/icon_search.svg');

  /// File path: assets/svg/icon_student.svg
  SvgGenImage get iconStudent => const SvgGenImage('assets/svg/icon_student.svg');

  /// File path: assets/svg/icon_success.svg
  SvgGenImage get iconSuccess => const SvgGenImage('assets/svg/icon_success.svg');

  /// File path: assets/svg/icon_target.svg
  SvgGenImage get iconTarget => const SvgGenImage('assets/svg/icon_target.svg');

  /// File path: assets/svg/icon_transfer.svg
  SvgGenImage get iconTransfer => const SvgGenImage('assets/svg/icon_transfer.svg');

  /// File path: assets/svg/logo_lunion_educ_text.svg
  SvgGenImage get logoLunionEducText => const SvgGenImage('assets/svg/logo_lunion_educ_text.svg');

  /// Directory path: assets/svg/menu
  $AssetsSvgMenuGen get menu => const $AssetsSvgMenuGen();

  /// File path: assets/svg/onboarding_1.svg
  SvgGenImage get onboarding1 => const SvgGenImage('assets/svg/onboarding_1.svg');

  /// File path: assets/svg/onboarding_2.svg
  SvgGenImage get onboarding2 => const SvgGenImage('assets/svg/onboarding_2.svg');

  /// File path: assets/svg/onboarding_3.svg
  SvgGenImage get onboarding3 => const SvgGenImage('assets/svg/onboarding_3.svg');

  /// File path: assets/svg/sorry_no_information.svg
  SvgGenImage get sorryNoInformation => const SvgGenImage('assets/svg/sorry_no_information.svg');

  /// File path: assets/svg/welcome_finished_register.svg
  SvgGenImage get welcomeFinishedRegister => const SvgGenImage('assets/svg/welcome_finished_register.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    arrowLeft,
    bookmark,
    confirmationPaiement,
    iconBell,
    iconClean,
    iconDownload,
    iconError,
    iconGlitter,
    iconInscription,
    iconInscriptionMenu,
    iconLunionEduc,
    iconPaintPalette,
    iconParent,
    iconPicture,
    iconProject,
    iconRegistrationSuccess,
    iconReinscription,
    iconSearch,
    iconStudent,
    iconSuccess,
    iconTarget,
    iconTransfer,
    logoLunionEducText,
    onboarding1,
    onboarding2,
    onboarding3,
    sorryNoInformation,
    welcomeFinishedRegister,
  ];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/en-UK.json
  String get enUK => 'assets/translations/en-UK.json';

  /// File path: assets/translations/fr-FR.json
  String get frFR => 'assets/translations/fr-FR.json';

  /// List of all assets
  List<String> get values => [enUK, frFR];
}

class $AssetsImagesFlagsGen {
  const $AssetsImagesFlagsGen();

  /// File path: assets/images/flags/bj.png
  AssetGenImage get bj => const AssetGenImage('assets/images/flags/bj.png');

  /// File path: assets/images/flags/ci.png
  AssetGenImage get ci => const AssetGenImage('assets/images/flags/ci.png');

  /// File path: assets/images/flags/ml.png
  AssetGenImage get ml => const AssetGenImage('assets/images/flags/ml.png');

  /// File path: assets/images/flags/sn.png
  AssetGenImage get sn => const AssetGenImage('assets/images/flags/sn.png');

  /// File path: assets/images/flags/tg.png
  AssetGenImage get tg => const AssetGenImage('assets/images/flags/tg.png');

  /// List of all assets
  List<AssetGenImage> get values => [bj, ci, ml, sn, tg];
}

class $AssetsSvgMenuGen {
  const $AssetsSvgMenuGen();

  /// File path: assets/svg/menu/icon_cours.svg
  SvgGenImage get iconCours => const SvgGenImage('assets/svg/menu/icon_cours.svg');

  /// File path: assets/svg/menu/icon_favoris.svg
  SvgGenImage get iconFavoris => const SvgGenImage('assets/svg/menu/icon_favoris.svg');

  /// File path: assets/svg/menu/icon_home_selected.svg
  SvgGenImage get iconHomeSelected => const SvgGenImage('assets/svg/menu/icon_home_selected.svg');

  /// File path: assets/svg/menu/icon_menu.svg
  SvgGenImage get iconMenu => const SvgGenImage('assets/svg/menu/icon_menu.svg');

  /// File path: assets/svg/menu/icon_school.svg
  SvgGenImage get iconSchool => const SvgGenImage('assets/svg/menu/icon_school.svg');

  /// List of all assets
  List<SvgGenImage> get values => [iconCours, iconFavoris, iconHomeSelected, iconMenu, iconSchool];
}

class Assets {
  const Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsSvgGen svg = $AssetsSvgGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}}) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(_assetName, assetBundle: bundle, packageName: package);
    } else {
      loader = _svg.SvgAssetLoader(_assetName, assetBundle: bundle, packageName: package, theme: theme);
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ?? (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
