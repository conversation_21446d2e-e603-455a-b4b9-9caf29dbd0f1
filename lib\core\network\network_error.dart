class NetworkError {
  final bool? success;
  final String? message;
  final String? errors;

  NetworkError({this.success, this.message, this.errors});

  factory NetworkError.fromJson({required Map<String, dynamic> json}) {
    return NetworkError(
      success: json['success'],
      message: json['message'],
      errors: json['errors'],
    );
  }

  @override
  String toString() {
    return "NetworkError - statusCode: $errors - message: $message";
  }
}
